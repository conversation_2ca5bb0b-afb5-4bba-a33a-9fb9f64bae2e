/*
Theme Name: Nols ESPA Theme Two
Description: A WordPress theme inspired by Papua New Guinea's East Sepik Province cultural heritage, featuring traditional colors and design elements from the PNG flag and cultural motifs.
Author: <PERSON><PERSON>
Author URI: https://www.dakoiims.com
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: nols-espa-theme-two
Tags: cultural, traditional, responsive, blog, custom-header, custom-menu, featured-images, threaded-comments
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --png-red: #CE1126;
    --png-green: #006A4E;
    --png-yellow: #FFD700;
    --dark-green: #004d3a;
    --light-green: #00a86b;
    --cream: #FFF8DC;
    --dark-brown: #8B4513;
}

body {
    font-family: 'Georgia', serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, var(--png-green) 0%, var(--dark-green) 100%);
    min-height: 100vh;
}

/* WordPress Core Styles */
.alignleft {
    float: left;
    margin-right: 1rem;
    margin-bottom: 1rem;
}

.alignright {
    float: right;
    margin-left: 1rem;
    margin-bottom: 1rem;
}

.aligncenter {
    display: block;
    margin: 0 auto 1rem;
}

.wp-caption {
    max-width: 100%;
    margin-bottom: 1rem;
}

.wp-caption-text {
    font-size: 0.9rem;
    color: #666;
    text-align: center;
    padding: 0.5rem;
}

.screen-reader-text {
    position: absolute !important;
    clip: rect(1px, 1px, 1px, 1px);
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Header */
.site-header {
    background: linear-gradient(45deg, var(--png-green) 0%, var(--png-green) 50%, var(--png-red) 50%, var(--png-red) 100%);
    color: white;
    padding: 1rem 0;
    position: relative;
    overflow: hidden;
}

.site-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,20 Q50,5 80,20 Q65,50 80,80 Q50,65 20,80 Q35,50 20,20 Z" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="2"/></svg>') repeat;
    opacity: 0.3;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.site-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-icon {
    width: 60px;
    height: 60px;
    background: var(--png-yellow);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--png-red);
    border: 3px solid white;
}

.site-title {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin: 0;
}

.site-title a {
    color: white;
    text-decoration: none;
}

.site-description {
    font-size: 1rem;
    opacity: 0.9;
    font-style: italic;
    margin: 0;
}

/* Navigation */
.main-nav {
    background: rgba(0, 106, 78, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 3px solid var(--png-yellow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 0.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
    padding: 1rem 0;
    margin: 0;
}

.nav-item a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-item a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--png-yellow), transparent);
    transition: left 0.5s;
}

.nav-item a:hover::before {
    left: 100%;
}

.nav-item a:hover {
    background: var(--png-red);
    color: white;
    transform: translateY(-2px);
}

/* Main Content */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.5rem;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 0.5rem;
}

.content-area {
    background: var(--cream);
    border-radius: 15px;
    padding: 0.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.content-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--png-red), var(--png-yellow), var(--png-green));
}

/* Posts */
.post, article {
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid var(--light-green);
}

.post:last-child, article:last-child {
    border-bottom: none;
}

.post-title, .entry-title {
    color: var(--png-red);
    font-size: 2rem;
    margin-bottom: 1rem;
    position: relative;
}

.post-title::after, .entry-title::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--png-yellow);
}

.post-title a, .entry-title a {
    color: var(--png-red);
    text-decoration: none;
}

.post-title a:hover, .entry-title a:hover {
    color: var(--dark-green);
}

.post-meta, .entry-meta {
    color: var(--dark-green);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-style: italic;
}

.post-content, .entry-content {
    line-height: 1.8;
    color: #444;
}

.read-more, .more-link {
    display: inline-block;
    margin-top: 1rem;
    padding: 0.5rem 1.5rem;
    background: var(--png-green);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.read-more:hover, .more-link:hover {
    background: var(--png-red);
    transform: translateX(5px);
    color: white;
}

/* Sidebar */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.widget {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 0.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-left: 5px solid var(--png-yellow);
    margin-bottom: 0.5rem;
}

.widget-title {
    color: var(--png-red);
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;
}

.widget-title::before,
.widget-title::after {
    content: '◆';
    color: var(--png-yellow);
    font-size: 0.8rem;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.widget-title::before {
    left: 0;
}

.widget-title::after {
    right: 0;
}

.widget-content {
    margin-top: 1rem;
}

.widget-content ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.widget-content li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #eee;
    line-height: 1.5;
}

.widget-content li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.widget-content li:first-child {
    padding-top: 0;
}

.widget-content a {
    color: var(--dark-green);
    text-decoration: none;
    transition: color 0.3s ease;
}

.widget-content a:hover {
    color: var(--png-red);
}

/* Cultural Pattern Widget */
.cultural-pattern {
    background: linear-gradient(45deg, var(--png-red), var(--png-yellow));
    height: 120px;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    margin-top: 1rem;
}

.cultural-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 50 50"><circle cx="25" cy="25" r="10" fill="none" stroke="rgba(0,106,78,0.4)" stroke-width="2"/><path d="M15,15 L35,35 M35,15 L15,35" stroke="rgba(0,106,78,0.4)" stroke-width="1"/></svg>') repeat;
}

/* Widget spacing improvements */
.widget p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.widget p:last-child {
    margin-bottom: 0;
}

/* Search widget spacing */
.widget .search-form {
    margin-top: 0.5rem;
}

/* Archives and categories spacing */
.widget-content .post-count {
    color: var(--png-green);
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

.widget-content .post-date {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

/* Footer */
.site-footer {
    background: var(--dark-green);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
    position: relative;
}

.site-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--png-red), var(--png-yellow), var(--png-green));
}

/* Comments */
.comments-area {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid var(--light-green);
}

.comments-title {
    color: var(--png-red);
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.comment-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.comment {
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 10px;
    border-left: 3px solid var(--png-yellow);
}

.comment-author {
    font-weight: bold;
    color: var(--dark-green);
}

.comment-meta {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.comment-content {
    line-height: 1.6;
}

/* Forms */
.comment-form input,
.comment-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--light-green);
    border-radius: 5px;
    font-family: inherit;
    margin-bottom: 1rem;
}

.comment-form input:focus,
.comment-form textarea:focus {
    outline: none;
    border-color: var(--png-red);
}

.comment-form input[type="submit"] {
    background: var(--png-green);
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    transition: background 0.3s ease;
    width: auto;
}

.comment-form input[type="submit"]:hover {
    background: var(--png-red);
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .header-content {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .site-title {
        font-size: 1.8rem;
    }

    .site-description {
        font-size: 0.9rem;
    }

    .main-nav {
        position: relative;
    }

    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--png-green);
        flex-direction: column;
        gap: 0;
        padding: 0;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .nav-menu.is-open {
        max-height: 400px;
        padding: 1rem 0;
    }

    .nav-item {
        width: 100%;
        text-align: center;
    }

    .nav-item a {
        display: block;
        padding: 1rem;
        border-radius: 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-item:last-child a {
        border-bottom: none;
    }

    .main-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem 0.5rem;
    }

    .content-area {
        padding: 1.5rem;
    }

    .post-title, .entry-title {
        font-size: 1.5rem;
    }

    .author-info {
        flex-direction: column;
        text-align: center;
    }

    .nav-links {
        flex-direction: column;
        gap: 1rem;
    }

    .related-posts-grid {
        grid-template-columns: 1fr;
    }

    .suggestions-grid {
        grid-template-columns: 1fr;
    }

    .footer-widgets-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .site-title {
        font-size: 1.5rem;
    }

    .logo-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .content-area {
        padding: 1rem;
    }

    .post-title, .entry-title {
        font-size: 1.3rem;
    }

    .sidebar {
        gap: 1rem;
    }

    .widget {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .content-area {
        padding: 1rem;
    }

    .widget-title {
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }

    .widget-content li {
        padding: 0.5rem 0;
    }

    .related-post-content {
        padding: 0.75rem;
    }

    .suggestion-box {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.post, article {
    animation: fadeInUp 0.6s ease-out;
}

.widget {
    animation: fadeInUp 0.8s ease-out;
}

/* Bird of Paradise Animation */
@keyframes fly {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
}

.logo-icon {
    animation: fly 3s ease-in-out infinite;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 1rem;
    background: var(--png-green);
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.pagination a:hover {
    background: var(--png-red);
}

.pagination .current {
    background: var(--png-red);
}

/* Search Form */
.search-form {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.search-form input[type="search"] {
    flex: 1;
    padding: 0.5rem;
    border: 2px solid var(--light-green);
    border-radius: 5px;
}

.search-form input[type="submit"] {
    padding: 0.5rem 1rem;
    background: var(--png-green);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.search-form input[type="submit"]:hover {
    background: var(--png-red);
}

/* Archive and Category Pages */
.archive-title,
.page-title {
    color: var(--png-red);
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.archive-title::after,
.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: var(--png-yellow);
}

/* 404 Page */
.error-404 .page-content {
    text-align: center;
    padding: 3rem 0;
}

.error-404 .page-title {
    font-size: 4rem;
    color: var(--png-red);
    margin-bottom: 1rem;
}

/* Accessibility */
.skip-link {
    position: absolute;
    left: -9999px;
    top: 0;
    z-index: 999999;
    padding: 8px 16px;
    background: var(--png-red);
    color: white;
    text-decoration: none;
}

.skip-link:focus {
    left: 6px;
    top: 7px;
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    background: var(--png-yellow);
    color: var(--png-red);
    border: none;
    padding: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.menu-toggle:hover {
    background: var(--png-red);
    color: white;
}

.menu-toggle .menu-icon {
    display: block;
    font-size: 1.5rem;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--png-red);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: bold;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.back-to-top:hover {
    background: var(--png-green);
    transform: translateY(-3px);
    color: white;
}

/* Search Form Enhancements */
.search-form {
    position: relative;
}

.search-clear {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem;
}

.search-clear:hover {
    color: var(--png-red);
}

/* Post Thumbnails */
.post-thumbnail,
.search-thumbnail {
    margin-bottom: 1rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.post-thumbnail img,
.search-thumbnail img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.post-thumbnail:hover img,
.search-thumbnail:hover img {
    transform: scale(1.05);
}

.post-thumbnail-caption {
    padding: 0.5rem;
    background: rgba(0,0,0,0.7);
    color: white;
    font-size: 0.9rem;
    text-align: center;
}

/* Post Navigation */
.post-navigation {
    margin: 2rem 0;
    padding: 1rem 0;
    border-top: 2px solid var(--light-green);
    border-bottom: 2px solid var(--light-green);
}

.nav-links {
    display: flex;
    justify-content: space-between;
    gap: 2rem;
}

.nav-previous,
.nav-next {
    flex: 1;
}

.nav-previous a,
.nav-next a {
    display: block;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    text-decoration: none;
    color: var(--dark-green);
    transition: all 0.3s ease;
    border-left: 4px solid var(--png-yellow);
}

.nav-previous a:hover,
.nav-next a:hover {
    background: var(--cream);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.nav-subtitle {
    display: block;
    font-size: 0.9rem;
    color: var(--png-red);
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.nav-title {
    display: block;
    font-size: 1.1rem;
    line-height: 1.4;
}

/* Author Info */
.author-info {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    margin: 2rem 0;
    border-left: 4px solid var(--png-green);
}

.author-avatar img {
    border-radius: 50%;
    border: 3px solid var(--png-yellow);
}

.author-description {
    flex: 1;
}

.author-title {
    color: var(--png-red);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.author-bio {
    line-height: 1.6;
    margin-bottom: 1rem;
}

.author-link a {
    color: var(--dark-green);
    text-decoration: none;
    font-weight: bold;
}

.author-link a:hover {
    color: var(--png-red);
}

/* Related Posts */
.related-posts {
    margin: 3rem 0;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border-top: 4px solid var(--png-green);
}

.related-posts-title {
    color: var(--png-red);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
    position: relative;
}

.related-posts-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--png-yellow);
}

.related-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.related-post {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.related-post:hover {
    transform: translateY(-5px);
}

.related-post-thumbnail img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.related-post-content {
    padding: 1rem;
}

.related-post-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.related-post-title a {
    color: var(--dark-green);
    text-decoration: none;
}

.related-post-title a:hover {
    color: var(--png-red);
}

.related-post-meta {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.related-post-excerpt {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #555;
}

/* Tag Cloud */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-link {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: var(--light-green);
    color: white;
    text-decoration: none;
    border-radius: 15px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.tag-link:hover {
    background: var(--png-red);
    transform: translateY(-2px);
    color: white;
}

/* Search Results */
.search-results-info {
    background: var(--png-yellow);
    color: var(--dark-green);
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 2rem;
    font-weight: bold;
    text-align: center;
}

.search-result mark {
    background: var(--png-yellow);
    color: var(--dark-green);
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
}

.search-form-container {
    background: rgba(255, 255, 255, 0.9);
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

/* 404 Page Styles */
.error-404 .page-content {
    text-align: center;
}

.error-message {
    margin-bottom: 2rem;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: fly 3s ease-in-out infinite;
}

.error-search,
.error-suggestions {
    background: rgba(255, 255, 255, 0.9);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.suggestion-box {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid var(--png-yellow);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.suggestion-box h4 {
    color: var(--png-red);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.suggestion-box ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.suggestion-box li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.suggestion-box li:last-child {
    border-bottom: none;
}

.suggestion-box a {
    color: var(--dark-green);
    text-decoration: none;
    transition: color 0.3s ease;
}

.suggestion-box a:hover {
    color: var(--png-red);
}

/* Footer Widgets */
.footer-widgets-area {
    background: rgba(0, 77, 58, 0.9);
    padding: 2rem 0;
    color: white;
}

.footer-widgets-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.footer-widget {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    border-top: 3px solid var(--png-yellow);
}

.footer-widget-title {
    color: var(--png-yellow);
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.footer-widget ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-widget li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-widget li:last-child {
    border-bottom: none;
}

.footer-widget a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-widget a:hover {
    color: var(--png-yellow);
}

/* Print Styles */
@media print {
    .site-header,
    .main-nav,
    .sidebar,
    .site-footer,
    .back-to-top,
    .menu-toggle,
    .comment-form,
    .post-navigation,
    .related-posts {
        display: none !important;
    }

    .main-container {
        grid-template-columns: 1fr;
        max-width: none;
        margin: 0;
        padding: 0;
    }

    .content-area {
        background: white;
        box-shadow: none;
        border-radius: 0;
        padding: 0;
    }

    .post, article {
        page-break-inside: avoid;
        margin-bottom: 2rem;
        border-bottom: 1px solid #ccc;
    }

    .entry-title {
        color: #000;
        font-size: 18pt;
    }

    .entry-content {
        font-size: 12pt;
        line-height: 1.5;
        color: #000;
    }

    a {
        color: #000;
        text-decoration: underline;
    }

    .post-thumbnail img {
        max-width: 100%;
        height: auto;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --png-red: #B71C1C;
        --png-green: #1B5E20;
        --png-yellow: #F57F17;
        --dark-green: #000000;
        --light-green: #2E7D32;
        --cream: #FFFFFF;
    }

    .site-header,
    .main-nav,
    .content-area,
    .widget {
        border: 2px solid #000;
    }

    .nav-item a:hover,
    .read-more:hover,
    .more-link:hover {
        outline: 2px solid #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .logo-icon {
        animation: none;
    }

    .post,
    .widget {
        animation: none;
    }
}

/* Focus Styles for Better Accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 3px solid var(--png-yellow);
    outline-offset: 2px;
}

.nav-item a:focus {
    background: var(--png-red);
    color: white;
}

.menu-toggle:focus {
    outline: 3px solid white;
    outline-offset: 2px;
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--cream);
}

::-webkit-scrollbar-thumb {
    background: var(--png-green);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--png-red);
}

/* Selection Styles */
::selection {
    background: var(--png-yellow);
    color: var(--dark-green);
}

::-moz-selection {
    background: var(--png-yellow);
    color: var(--dark-green);
}

/* Front Page Styles */
.front-page-posts {
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border-top: 4px solid var(--png-green);
}

.section-title {
    color: var(--png-red);
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: var(--png-yellow);
}

.front-page-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.front-page-post {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.front-page-post:hover {
    transform: translateY(-5px);
}

.front-page-post-thumbnail img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.front-page-post-content {
    padding: 1.5rem;
}

.front-page-post-title {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.front-page-post-title a {
    color: var(--dark-green);
    text-decoration: none;
}

.front-page-post-title a:hover {
    color: var(--png-red);
}

.front-page-post-meta {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
}

.front-page-post-excerpt {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #555;
}

.front-page-read-more {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--light-green);
    color: white;
    text-decoration: none;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.front-page-read-more:hover {
    background: var(--png-red);
    transform: translateX(3px);
    color: white;
}

.front-page-posts-link {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 2px solid var(--light-green);
}

/* Mobile Responsive for Front Page */
@media (max-width: 768px) {
    .front-page-posts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .front-page-post-content {
        padding: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
    }
}
