# Nols ESPA Theme Two

A WordPress theme inspired by Papua New Guinea's East Sepik Province cultural heritage, featuring traditional colors and design elements from the PNG flag and cultural motifs.

## Theme Information

- **Theme Name:** Nols ESPA Theme Two
- **Author:** <PERSON><PERSON>
- **Website:** https://www.dakoiims.com
- **Email:** <EMAIL>
- **Version:** 1.0.0
- **License:** GPL v2 or later

## Description

This theme celebrates the rich cultural tapestry of East Sepik Province, Papua New Guinea. It incorporates the vibrant colors of the PNG flag (red, green, and yellow) with traditional design elements and cultural motifs to create a modern, responsive WordPress theme that honors Papua New Guinea's heritage.

## Features

### Design Elements
- **Cultural Color Scheme:** PNG flag colors (Red #CE1126, Green #006A4E, Yellow #FFD700)
- **Traditional Patterns:** Subtle geometric patterns throughout the design
- **Bird of Paradise Logo:** Animated icon representing the national symbol
- **Cultural Typography:** Elegant serif fonts with decorative elements
- **Responsive Layout:** Adapts to all screen sizes and devices

### WordPress Features
- **Custom Header:** Support for custom header images and colors
- **Custom Logo:** Upload your own logo or use the default Bird of Paradise icon
- **Navigation Menus:** Primary and footer menu locations
- **Widget Areas:** Primary sidebar and footer widget areas
- **Post Thumbnails:** Featured image support for posts and pages
- **Custom Comments:** Styled comment system with cultural design elements
- **Search Functionality:** Enhanced search with highlighting
- **Pagination:** Styled pagination for archives and search results

### Cultural Widgets
- **Cultural Pattern Widget:** Displays traditional design patterns
- **Recent Posts:** Styled list of recent posts
- **Categories:** Cultural categories with post counts
- **Tag Cloud:** Visual tag display with cultural styling
- **Search Widget:** Enhanced search form

### Customization Options
- **Theme Customizer:** Customize colors, header, and background
- **Cultural Colors:** Adjust PNG flag colors to your preference
- **Custom Background:** Set custom background colors or images
- **Typography:** Georgia serif font family for cultural authenticity

## Installation

1. Download the theme files
2. Upload to your WordPress `/wp-content/themes/` directory
3. Activate the theme in WordPress Admin > Appearance > Themes
4. Customize via Appearance > Customize

## Setup Recommendations

### Menus
1. Go to Appearance > Menus
2. Create a new menu and assign it to "Primary Menu"
3. Add pages like: Home, Culture, Traditions, Events, Gallery, Contact

### Widgets
1. Go to Appearance > Widgets
2. Add widgets to "Primary Sidebar" such as:
   - Cultural Pattern
   - Recent Posts
   - Categories
   - Search
   - Tag Cloud

### Customization
1. Go to Appearance > Customize
2. Set your site title and tagline
3. Upload a custom logo (recommended: 60x60px)
4. Adjust cultural colors if desired
5. Set a custom header image (recommended: 1200x400px)

## File Structure

```
nols-espa-theme-two/
├── style.css              # Main stylesheet with theme info
├── index.php              # Main template file
├── functions.php          # Theme functions and features
├── header.php             # Header template
├── footer.php             # Footer template
├── sidebar.php            # Sidebar template
├── single.php             # Single post template
├── page.php               # Page template
├── archive.php            # Archive template
├── search.php             # Search results template
├── 404.php                # 404 error page template
├── comments.php           # Comments template
├── searchform.php         # Search form template
├── js/
│   └── theme.js           # Theme JavaScript
├── dev_guide/
│   └── coding_guide.md    # Original design guide
└── README.md              # This file
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Cultural Significance

This theme draws inspiration from:
- **PNG Flag Colors:** Red, green, and yellow representing the nation
- **Bird of Paradise:** National bird symbol
- **Traditional Patterns:** Geometric designs common in PNG art
- **East Sepik Province:** Known for intricate wood carvings and ceremonies
- **Sepik River:** The mighty river that flows through the province

## Customization

### Colors
The theme uses CSS custom properties for easy color customization:
```css
:root {
    --png-red: #CE1126;
    --png-green: #006A4E;
    --png-yellow: #FFD700;
    --dark-green: #004d3a;
    --light-green: #00a86b;
    --cream: #FFF8DC;
}
```

### Fonts
The theme uses Georgia serif font for cultural authenticity. You can change this in the CSS:
```css
body {
    font-family: 'Georgia', serif;
}
```

## Support

For support, please contact:
- **Email:** <EMAIL>
- **Website:** https://www.dakoiims.com

## Changelog

### Version 1.0.0
- Initial release
- Cultural design implementation
- Responsive layout
- WordPress standard features
- Custom widgets and functionality

## License

This theme is licensed under the GPL v2 or later.
http://www.gnu.org/licenses/gpl-2.0.html

## Credits

- **Design Inspiration:** Papua New Guinea flag and East Sepik Province cultural heritage
- **Author:** Noland Gande
- **Cultural Consultation:** East Sepik Province community
- **Typography:** Georgia serif font family
- **Icons:** Unicode symbols and custom CSS patterns
