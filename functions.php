<?php
/**
 * Nols ESPA Theme Two functions and definitions
 *
 * @package Nols_ESPA_Theme_Two
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme setup
 */
function nols_espa_theme_setup() {
    // Add default posts and comments RSS feed links to head
    add_theme_support('automatic-feed-links');

    // Let WordPress manage the document title
    add_theme_support('title-tag');

    // Enable support for Post Thumbnails on posts and pages
    add_theme_support('post-thumbnails');

    // Add support for responsive embedded content
    add_theme_support('responsive-embeds');

    // Add support for editor styles
    add_theme_support('editor-styles');

    // Add support for wide alignment
    add_theme_support('align-wide');

    // Add support for custom logo
    add_theme_support('custom-logo', array(
        'height'      => 60,
        'width'       => 60,
        'flex-width'  => true,
        'flex-height' => true,
    ));

    // Add support for custom header
    add_theme_support('custom-header', array(
        'default-color' => 'CE1126',
        'width'         => 1200,
        'height'        => 400,
        'flex-width'    => true,
        'flex-height'   => true,
    ));

    // Add support for custom background
    add_theme_support('custom-background', array(
        'default-color' => '006A4E',
    ));

    // Add support for HTML5 markup
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script',
    ));

    // Add support for selective refresh for widgets
    add_theme_support('customize-selective-refresh-widgets');

    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', 'nols-espa-theme-two'),
        'footer'  => esc_html__('Footer Menu', 'nols-espa-theme-two'),
    ));

    // Set content width
    if (!isset($content_width)) {
        $content_width = 800;
    }
}
add_action('after_setup_theme', 'nols_espa_theme_setup');

/**
 * Register widget areas
 */
function nols_espa_theme_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Primary Sidebar', 'nols-espa-theme-two'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here to appear in your sidebar.', 'nols-espa-theme-two'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));

    register_sidebar(array(
        'name'          => esc_html__('Footer Widgets', 'nols-espa-theme-two'),
        'id'            => 'footer-widgets',
        'description'   => esc_html__('Add widgets here to appear in your footer.', 'nols-espa-theme-two'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="footer-widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'nols_espa_theme_widgets_init');

/**
 * Enqueue scripts and styles
 */
function nols_espa_theme_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('nols-espa-theme-style', get_stylesheet_uri(), array(), '1.0.0');

    // Enqueue Google Fonts
    wp_enqueue_style('nols-espa-theme-fonts', 'https://fonts.googleapis.com/css2?family=Georgia:wght@400;700&display=swap', array(), null);

    // Enqueue theme JavaScript
    wp_enqueue_script('nols-espa-theme-script', get_template_directory_uri() . '/js/theme.js', array(), '1.0.0', true);

    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'nols_espa_theme_scripts');

/**
 * Custom excerpt length
 */
function nols_espa_theme_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'nols_espa_theme_excerpt_length');

/**
 * Custom excerpt more
 */
function nols_espa_theme_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'nols_espa_theme_excerpt_more');

/**
 * Add custom classes to body
 */
function nols_espa_theme_body_classes($classes) {
    // Add class for cultural theme
    $classes[] = 'cultural-theme';
    
    // Add class for PNG colors
    $classes[] = 'png-colors';
    
    return $classes;
}
add_filter('body_class', 'nols_espa_theme_body_classes');

/**
 * Customize comment form
 */
function nols_espa_theme_comment_form_defaults($defaults) {
    $defaults['comment_notes_before'] = '<p class="comment-notes">' . esc_html__('Your email address will not be published. Required fields are marked *', 'nols-espa-theme-two') . '</p>';
    $defaults['comment_notes_after'] = '';
    $defaults['title_reply'] = esc_html__('Leave a Comment', 'nols-espa-theme-two');
    $defaults['title_reply_to'] = esc_html__('Leave a Reply to %s', 'nols-espa-theme-two');
    $defaults['cancel_reply_link'] = esc_html__('Cancel Reply', 'nols-espa-theme-two');
    $defaults['label_submit'] = esc_html__('Post Comment', 'nols-espa-theme-two');
    
    return $defaults;
}
add_filter('comment_form_defaults', 'nols_espa_theme_comment_form_defaults');

/**
 * Add cultural pattern widget
 */
class Nols_ESPA_Cultural_Pattern_Widget extends WP_Widget {
    
    public function __construct() {
        parent::__construct(
            'nols_espa_cultural_pattern',
            esc_html__('Cultural Pattern', 'nols-espa-theme-two'),
            array('description' => esc_html__('Display a cultural pattern design element.', 'nols-espa-theme-two'))
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        
        echo '<div class="cultural-pattern"></div>';
        
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : esc_html__('Cultural Pattern', 'nols-espa-theme-two');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php esc_attr_e('Title:', 'nols-espa-theme-two'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" value="<?php echo esc_attr($title); ?>">
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        return $instance;
    }
}

/**
 * Register cultural pattern widget
 */
function nols_espa_theme_register_widgets() {
    register_widget('Nols_ESPA_Cultural_Pattern_Widget');
}
add_action('widgets_init', 'nols_espa_theme_register_widgets');

/**
 * Add theme customizer options
 */
function nols_espa_theme_customize_register($wp_customize) {
    // Add cultural colors section
    $wp_customize->add_section('nols_espa_cultural_colors', array(
        'title'    => esc_html__('Cultural Colors', 'nols-espa-theme-two'),
        'priority' => 30,
    ));
    
    // PNG Red color
    $wp_customize->add_setting('png_red_color', array(
        'default'           => '#CE1126',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_red_color', array(
        'label'    => esc_html__('PNG Red Color', 'nols-espa-theme-two'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_red_color',
    )));
    
    // PNG Green color
    $wp_customize->add_setting('png_green_color', array(
        'default'           => '#006A4E',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_green_color', array(
        'label'    => esc_html__('PNG Green Color', 'nols-espa-theme-two'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_green_color',
    )));
    
    // PNG Yellow color
    $wp_customize->add_setting('png_yellow_color', array(
        'default'           => '#FFD700',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'png_yellow_color', array(
        'label'    => esc_html__('PNG Yellow Color', 'nols-espa-theme-two'),
        'section'  => 'nols_espa_cultural_colors',
        'settings' => 'png_yellow_color',
    )));
}
add_action('customize_register', 'nols_espa_theme_customize_register');
